/**
 * Wizard Character Animator with Live2D Style Animations
 * Integrates with text-to-speech for realistic lip-sync
 */

class WizardAnimator {
    constructor(svgElementId) {
        this.svgElement = document.getElementById(svgElementId);
        this.mouthElement = null;
        this.isAnimating = false;
        this.currentAnimation = null;
        this.speechSynthesis = window.speechSynthesis;
        
        // Lip-sync mapping for different phonemes
        this.phonemeMap = {
            'a': 'mouth-a',
            'e': 'mouth-e', 
            'i': 'mouth-i',
            'o': 'mouth-o',
            'u': 'mouth-u',
            'closed': 'mouth-closed'
        };
        
        this.init();
    }
    
    init() {
        if (!this.svgElement) {
            console.error('SVG element not found');
            return;
        }
        
        // Find mouth element in SVG
        this.mouthElement = this.svgElement.querySelector('.mouth-group');
        
        if (!this.mouthElement) {
            console.warn('Mouth element not found in SVG');
        }
        
        console.log('🧙‍♂️ Wizard Animator initialized');
    }
    
    /**
     * Speak text with lip-sync animation
     */
    speak(text, options = {}) {
        return new Promise((resolve, reject) => {
            if (!this.speechSynthesis) {
                console.error('Speech synthesis not supported');
                reject(new Error('Speech synthesis not supported'));
                return;
            }
            
            const utterance = new SpeechSynthesisUtterance(text);
            
            // Configure voice options
            utterance.rate = options.rate || 0.9;
            utterance.pitch = options.pitch || 1.1;
            utterance.volume = options.volume || 0.8;
            
            // Set voice (prefer Slovak/Czech if available)
            const voices = this.speechSynthesis.getVoices();
            const preferredVoice = voices.find(voice => 
                voice.lang.includes('sk') || voice.lang.includes('cs') || voice.lang.includes('en')
            );
            if (preferredVoice) {
                utterance.voice = preferredVoice;
            }
            
            // Start lip-sync animation
            utterance.onstart = () => {
                console.log('🗣️ Starting speech animation');
                this.startLipSync(text);
            };
            
            utterance.onend = () => {
                console.log('✅ Speech animation completed');
                this.stopLipSync();
                resolve();
            };
            
            utterance.onerror = (error) => {
                console.error('❌ Speech synthesis error:', error);
                this.stopLipSync();
                reject(error);
            };
            
            this.speechSynthesis.speak(utterance);
        });
    }
    
    /**
     * Start lip-sync animation based on text
     */
    startLipSync(text) {
        if (!this.mouthElement) return;
        
        this.isAnimating = true;
        
        // Simple phoneme detection and animation
        const words = text.toLowerCase().split(' ');
        let wordIndex = 0;
        
        const animateWord = () => {
            if (!this.isAnimating || wordIndex >= words.length) {
                return;
            }
            
            const word = words[wordIndex];
            this.animateWordPhonemes(word);
            
            wordIndex++;
            
            // Schedule next word (approximate timing)
            setTimeout(animateWord, word.length * 100 + 200);
        };
        
        animateWord();
    }
    
    /**
     * Animate phonemes for a single word
     */
    animateWordPhonemes(word) {
        if (!this.mouthElement) return;
        
        const phonemes = this.extractPhonemes(word);
        let phonemeIndex = 0;
        
        const animatePhoneme = () => {
            if (!this.isAnimating || phonemeIndex >= phonemes.length) {
                // Return to closed mouth
                this.setMouthShape('closed');
                return;
            }
            
            const phoneme = phonemes[phonemeIndex];
            this.setMouthShape(phoneme);
            
            phonemeIndex++;
            
            // Schedule next phoneme
            setTimeout(animatePhoneme, 120);
        };
        
        animatePhoneme();
    }
    
    /**
     * Extract phonemes from word (simplified)
     */
    extractPhonemes(word) {
        const phonemes = [];
        
        for (let i = 0; i < word.length; i++) {
            const char = word[i].toLowerCase();
            
            if ('aeiouáéíóúäöü'.includes(char)) {
                // Map vowels to mouth shapes
                if ('aá'.includes(char)) phonemes.push('a');
                else if ('eé'.includes(char)) phonemes.push('e');
                else if ('ií'.includes(char)) phonemes.push('i');
                else if ('oóö'.includes(char)) phonemes.push('o');
                else if ('uúü'.includes(char)) phonemes.push('u');
                else phonemes.push('a'); // default
            } else if (char.match(/[bcdfghjklmnpqrstvwxyz]/)) {
                // Consonants - brief closed mouth
                phonemes.push('closed');
            }
        }
        
        return phonemes.length > 0 ? phonemes : ['closed'];
    }
    
    /**
     * Set mouth shape for lip-sync
     */
    setMouthShape(phoneme) {
        if (!this.mouthElement) return;
        
        // Remove all mouth classes
        Object.values(this.phonemeMap).forEach(className => {
            this.mouthElement.classList.remove(className);
        });
        
        // Add new mouth shape class
        const className = this.phonemeMap[phoneme] || this.phonemeMap['closed'];
        this.mouthElement.classList.add(className);
    }
    
    /**
     * Stop lip-sync animation
     */
    stopLipSync() {
        this.isAnimating = false;
        
        if (this.mouthElement) {
            // Remove all mouth shape classes
            Object.values(this.phonemeMap).forEach(className => {
                this.mouthElement.classList.remove(className);
            });
        }
    }
    
    /**
     * Trigger specific animations
     */
    triggerAnimation(animationType) {
        switch (animationType) {
            case 'greeting':
                this.playGreetingAnimation();
                break;
            case 'thinking':
                this.playThinkingAnimation();
                break;
            case 'excited':
                this.playExcitedAnimation();
                break;
            case 'disappointed':
                this.playDisappointedAnimation();
                break;
            default:
                console.warn('Unknown animation type:', animationType);
        }
    }
    
    /**
     * Play greeting animation
     */
    playGreetingAnimation() {
        // Add temporary faster head movement
        const headElement = this.svgElement.querySelector('.head-group');
        if (headElement) {
            headElement.style.animationDuration = '2s';
            setTimeout(() => {
                headElement.style.animationDuration = '8s';
            }, 4000);
        }
    }
    
    /**
     * Play thinking animation
     */
    playThinkingAnimation() {
        const headElement = this.svgElement.querySelector('.head-tilt');
        if (headElement) {
            headElement.style.animationDuration = '3s';
            setTimeout(() => {
                headElement.style.animationDuration = '12s';
            }, 6000);
        }
    }
    
    /**
     * Play excited animation
     */
    playExcitedAnimation() {
        const eyeElements = this.svgElement.querySelectorAll('.eye-left, .eye-right');
        eyeElements.forEach(eye => {
            eye.style.animationDuration = '1s';
            setTimeout(() => {
                eye.style.animationDuration = '4s';
            }, 3000);
        });
    }
    
    /**
     * Play disappointed animation
     */
    playDisappointedAnimation() {
        const bodyElement = this.svgElement.querySelector('.body-group');
        if (bodyElement) {
            bodyElement.style.transform = 'scale(0.98) translateY(2px)';
            setTimeout(() => {
                bodyElement.style.transform = '';
            }, 2000);
        }
    }
    
    /**
     * Stop all speech
     */
    stopSpeaking() {
        if (this.speechSynthesis) {
            this.speechSynthesis.cancel();
        }
        this.stopLipSync();
    }
}

    /**
     * Integration methods for AlchemyGame
     */

    /**
     * Speak recipe instructions with appropriate animations
     */
    async speakRecipeStep(stepText, stepType = 'instruction') {
        // Trigger appropriate animation based on step type
        switch (stepType) {
            case 'weigh':
                this.triggerAnimation('thinking');
                break;
            case 'mix':
                this.triggerAnimation('excited');
                break;
            case 'wait':
                this.triggerAnimation('thinking');
                break;
            case 'success':
                this.triggerAnimation('excited');
                break;
            case 'failure':
                this.triggerAnimation('disappointed');
                break;
            default:
                // Default instruction animation
                break;
        }

        // Speak the text with lip-sync
        return this.speak(stepText, {
            rate: 0.85,
            pitch: 1.1,
            volume: 0.8
        });
    }

    /**
     * Quick greeting for game start
     */
    async greetPlayer() {
        this.triggerAnimation('greeting');
        return this.speak("Vitajte v mojej čarodejníckej dielni! Pripravte sa na magické dobrodružstvo!", {
            rate: 0.9,
            pitch: 1.15,
            volume: 0.85
        });
    }

    /**
     * Encourage player during game
     */
    async encouragePlayer() {
        this.triggerAnimation('excited');
        const encouragements = [
            "Výborne! Pokračujte v dobrej práci!",
            "Skvelé! Vaše čarodejnícke schopnosti sa zlepšujú!",
            "Perfektne! Ste na správnej ceste!",
            "Úžasné! Takto sa to robí!"
        ];

        const randomEncouragement = encouragements[Math.floor(Math.random() * encouragements.length)];
        return this.speak(randomEncouragement);
    }

    /**
     * Provide hint to player
     */
    async giveHint(hintText) {
        this.triggerAnimation('thinking');
        return this.speak(`Tip: ${hintText}`, {
            rate: 0.8,
            pitch: 1.0,
            volume: 0.75
        });
    }

    /**
     * Celebrate successful recipe completion
     */
    async celebrateSuccess(recipeName) {
        this.triggerAnimation('excited');
        return this.speak(`Gratulujeme! Úspešne ste vytvorili ${recipeName}! Ste skutočný čarodejník!`, {
            rate: 0.9,
            pitch: 1.2,
            volume: 0.9
        });
    }

    /**
     * Comfort player after failure
     */
    async comfortFailure() {
        this.triggerAnimation('disappointed');
        const comfortMessages = [
            "Nevadí, každý čarodejník sa musí učiť. Skúste to znovu!",
            "Chyby sú súčasťou učenia. Budete to mať nabudúce!",
            "Nič sa nedeje! Aj najlepší čarodejníci robia chyby.",
            "Skúste to ešte raz. Verím, že to zvládnete!"
        ];

        const randomComfort = comfortMessages[Math.floor(Math.random() * comfortMessages.length)];
        return this.speak(randomComfort, {
            rate: 0.8,
            pitch: 0.9,
            volume: 0.8
        });
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WizardAnimator;
}
