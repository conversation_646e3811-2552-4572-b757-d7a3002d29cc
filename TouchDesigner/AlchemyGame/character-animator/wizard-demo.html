<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Čarodej - Live2D <PERSON><PERSON></title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            font-family: 'Arial', sans-serif;
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            color: #ffd700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 30px;
        }
        
        .wizard-container {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 20px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,215,0,0.3);
        }
        
        #wizard-svg {
            max-width: 400px;
            height: auto;
            filter: drop-shadow(0 10px 20px rgba(0,0,0,0.3));
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }
        
        .control-group {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,215,0,0.2);
        }
        
        .control-group h3 {
            color: #ffd700;
            margin-top: 0;
        }
        
        button {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #1a1a2e;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255,215,0,0.3);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255,215,0,0.4);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        textarea {
            width: 100%;
            height: 80px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,215,0,0.3);
            border-radius: 10px;
            color: white;
            padding: 10px;
            font-family: inherit;
            resize: vertical;
        }
        
        textarea::placeholder {
            color: rgba(255,255,255,0.6);
        }
        
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 10px;
            background: rgba(0,255,0,0.1);
            border: 1px solid rgba(0,255,0,0.3);
        }
        
        .status.error {
            background: rgba(255,0,0,0.1);
            border-color: rgba(255,0,0,0.3);
        }
        
        .features {
            text-align: left;
            background: rgba(255,255,255,0.05);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .features ul {
            list-style: none;
            padding: 0;
        }
        
        .features li {
            padding: 5px 0;
            position: relative;
            padding-left: 25px;
        }
        
        .features li::before {
            content: "✨";
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧙‍♂️ Čarodej - Live2D Animácie Demo</h1>
        
        <div class="wizard-container">
            <!-- SVG will be loaded here -->
            <div id="svg-container"></div>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <h3>🗣️ Text-to-Speech</h3>
                <textarea id="speechText" placeholder="Napíšte text, ktorý má čarodej povedať...">Vitajte v mojej čarodejníckej dielni! Som Merlin a pomôžem vám vytvoriť magické elixíry.</textarea>
                <br>
                <button onclick="speakText()">Hovoriť</button>
                <button onclick="stopSpeaking()">Zastaviť</button>
            </div>
            
            <div class="control-group">
                <h3>🎭 Animácie</h3>
                <button onclick="triggerAnimation('greeting')">Pozdrav</button>
                <button onclick="triggerAnimation('thinking')">Premýšľanie</button>
                <button onclick="triggerAnimation('excited')">Nadšenie</button>
                <button onclick="triggerAnimation('disappointed')">Sklamanie</button>
            </div>
            
            <div class="control-group">
                <h3>🎵 Prednastavené texty</h3>
                <button onclick="speakPreset('intro')">Úvod</button>
                <button onclick="speakPreset('instructions')">Inštrukcie</button>
                <button onclick="speakPreset('success')">Úspech</button>
                <button onclick="speakPreset('failure')">Neúspech</button>
            </div>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div class="features">
            <h3>✨ Implementované funkcie:</h3>
            <ul>
                <li><strong>Realistické pohyby hlavy</strong> - jemné rotácie a posuny</li>
                <li><strong>Mrkanie očí</strong> - prirodzené mrkanie každé 4 sekundy</li>
                <li><strong>Pohyby úst pre reč</strong> - synchronizované s text-to-speech</li>
                <li><strong>Prirodzené idle animácie</strong> - dýchanie, pohyb očí, kolísanie rúcha</li>
                <li><strong>Lip-sync pre text-to-speech</strong> - automatická synchronizácia úst</li>
                <li><strong>Občasné naklonenie hlavy</strong> - pre prirodzenosť</li>
                <li><strong>Interaktívne animácie</strong> - rôzne emócie a stavy</li>
            </ul>
        </div>
    </div>

    <script src="wizard-animator.js"></script>
    <script>
        let wizardAnimator;
        
        // Prednastavené texty
        const presets = {
            intro: "Vitajte v mojej čarodejníckej dielni! Som Merlin a pomôžem vám vytvoriť magické elixíry. Pripravte sa na dobrodružstvo plné mágie!",
            instructions: "Pozorne sledujte recepty a pridávajte ingrediencie v správnom poradí. Každý krok je dôležitý pre úspešné vytvorenie elixíru.",
            success: "Výborne! Úspešne ste vytvorili magický elixír. Vaše čarodejnícke schopnosti sa zlepšujú!",
            failure: "Nevadí, každý čarodejník sa musí učiť. Skúste to znovu a buďte opatrnejší s ingredienciami."
        };
        
        // Load SVG and initialize animator
        async function loadWizard() {
            try {
                const response = await fetch('asset_dNSkJw3NBnEguAg7Y5kdz4Z7_vectorization_1752850777.svg');
                const svgText = await response.text();
                
                // Insert SVG into container
                const container = document.getElementById('svg-container');
                container.innerHTML = svgText;
                
                // Get the SVG element and set ID
                const svgElement = container.querySelector('svg');
                if (svgElement) {
                    svgElement.id = 'wizard-svg';
                    
                    // Initialize animator
                    wizardAnimator = new WizardAnimator('wizard-svg');
                    showStatus('✅ Čarodej načítaný a pripravený!', 'success');
                } else {
                    throw new Error('SVG element not found');
                }
            } catch (error) {
                console.error('Error loading wizard:', error);
                showStatus('❌ Chyba pri načítaní čarodeja: ' + error.message, 'error');
            }
        }
        
        // Speak custom text
        async function speakText() {
            if (!wizardAnimator) {
                showStatus('❌ Čarodej nie je načítaný', 'error');
                return;
            }
            
            const text = document.getElementById('speechText').value.trim();
            if (!text) {
                showStatus('❌ Zadajte text na vyslovenie', 'error');
                return;
            }
            
            try {
                showStatus('🗣️ Čarodej hovorí...', 'info');
                await wizardAnimator.speak(text);
                showStatus('✅ Reč dokončená', 'success');
            } catch (error) {
                console.error('Speech error:', error);
                showStatus('❌ Chyba pri reči: ' + error.message, 'error');
            }
        }
        
        // Stop speaking
        function stopSpeaking() {
            if (wizardAnimator) {
                wizardAnimator.stopSpeaking();
                showStatus('⏹️ Reč zastavená', 'info');
            }
        }
        
        // Speak preset text
        async function speakPreset(presetName) {
            if (!wizardAnimator) {
                showStatus('❌ Čarodej nie je načítaný', 'error');
                return;
            }
            
            const text = presets[presetName];
            if (!text) {
                showStatus('❌ Neznámy preset', 'error');
                return;
            }
            
            try {
                showStatus('🗣️ Čarodej hovorí...', 'info');
                await wizardAnimator.speak(text);
                showStatus('✅ Reč dokončená', 'success');
            } catch (error) {
                console.error('Speech error:', error);
                showStatus('❌ Chyba pri reči: ' + error.message, 'error');
            }
        }
        
        // Trigger animation
        function triggerAnimation(type) {
            if (!wizardAnimator) {
                showStatus('❌ Čarodej nie je načítaný', 'error');
                return;
            }
            
            wizardAnimator.triggerAnimation(type);
            showStatus(`🎭 Animácia "${type}" spustená`, 'info');
        }
        
        // Show status message
        function showStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = 'status ' + type;
            statusElement.style.display = 'block';
            
            // Auto-hide after 3 seconds
            setTimeout(() => {
                statusElement.style.display = 'none';
            }, 3000);
        }
        
        // Initialize when page loads
        window.addEventListener('load', loadWizard);
    </script>
</body>
</html>
