/**
 * AlchemyGame Integration for Wizard Character Animator
 * Connects the wizard animator with the main game logic
 */

class AlchemyGameWizardIntegration {
    constructor(gameApp) {
        this.gameApp = gameApp;
        this.wizardAnimator = null;
        this.isWizardReady = false;
        this.speechQueue = [];
        this.isProcessingQueue = false;
        
        this.init();
    }
    
    async init() {
        try {
            // Load wizard SVG into game
            await this.loadWizardIntoGame();
            
            // Initialize animator
            this.wizardAnimator = new WizardAnimator('game-wizard-svg');
            this.isWizardReady = true;
            
            // Connect to game events
            this.connectGameEvents();
            
            console.log('🧙‍♂️ Wizard integration initialized successfully');
            
            // Welcome message
            setTimeout(() => {
                this.wizardAnimator.greetPlayer();
            }, 1000);
            
        } catch (error) {
            console.error('❌ Failed to initialize wizard integration:', error);
        }
    }
    
    async loadWizardIntoGame() {
        try {
            // Create wizard container in game UI
            const gameContainer = document.querySelector('.game-container') || document.body;
            
            let wizardContainer = document.getElementById('game-wizard-container');
            if (!wizardContainer) {
                wizardContainer = document.createElement('div');
                wizardContainer.id = 'game-wizard-container';
                wizardContainer.className = 'wizard-character-container';
                wizardContainer.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    width: 200px;
                    height: 200px;
                    z-index: 1000;
                    pointer-events: none;
                    filter: drop-shadow(0 5px 15px rgba(0,0,0,0.3));
                `;
                gameContainer.appendChild(wizardContainer);
            }
            
            // Load SVG
            const response = await fetch('character-animator/asset_dNSkJw3NBnEguAg7Y5kdz4Z7_vectorization_1752850777.svg');
            const svgText = await response.text();
            
            wizardContainer.innerHTML = svgText;
            
            // Set ID for animator
            const svgElement = wizardContainer.querySelector('svg');
            if (svgElement) {
                svgElement.id = 'game-wizard-svg';
                svgElement.style.cssText = `
                    width: 100%;
                    height: 100%;
                `;
            }
            
        } catch (error) {
            console.error('Error loading wizard SVG:', error);
            throw error;
        }
    }
    
    connectGameEvents() {
        if (!this.gameApp) return;
        
        // Listen for game events
        document.addEventListener('gameStateChange', (event) => {
            this.handleGameStateChange(event.detail);
        });
        
        document.addEventListener('recipeStepComplete', (event) => {
            this.handleStepComplete(event.detail);
        });
        
        document.addEventListener('recipeComplete', (event) => {
            this.handleRecipeComplete(event.detail);
        });
        
        document.addEventListener('recipeFailed', (event) => {
            this.handleRecipeFailed(event.detail);
        });
        
        document.addEventListener('playerNeedsHelp', (event) => {
            this.handlePlayerNeedsHelp(event.detail);
        });
    }
    
    async handleGameStateChange(gameState) {
        if (!this.isWizardReady) return;
        
        switch (gameState.state) {
            case 'recipe_selected':
                await this.announceRecipe(gameState.recipe);
                break;
            case 'step_started':
                await this.announceStep(gameState.step);
                break;
            case 'game_paused':
                this.wizardAnimator.stopSpeaking();
                break;
        }
    }
    
    async handleStepComplete(stepData) {
        if (!this.isWizardReady) return;
        
        if (stepData.perfect) {
            await this.wizardAnimator.encouragePlayer();
        } else if (stepData.success) {
            await this.queueSpeech("Dobre! Pokračujte ďalej.", 'success');
        }
    }
    
    async handleRecipeComplete(recipeData) {
        if (!this.isWizardReady) return;
        
        await this.wizardAnimator.celebrateSuccess(recipeData.name);
    }
    
    async handleRecipeFailed(failureData) {
        if (!this.isWizardReady) return;
        
        await this.wizardAnimator.comfortFailure();
    }
    
    async handlePlayerNeedsHelp(helpData) {
        if (!this.isWizardReady) return;
        
        if (helpData.hint) {
            await this.wizardAnimator.giveHint(helpData.hint);
        }
    }
    
    async announceRecipe(recipe) {
        const announcement = `Vybrali ste si ${recipe.name}. ${recipe.description} Pripravte sa na čarovanie!`;
        await this.queueSpeech(announcement, 'instruction');
    }
    
    async announceStep(step) {
        let stepText = step.instruction;
        
        if (step.hint) {
            stepText += ` ${step.hint}`;
        }
        
        await this.wizardAnimator.speakRecipeStep(stepText, step.type);
    }
    
    async queueSpeech(text, type = 'instruction') {
        this.speechQueue.push({ text, type });
        
        if (!this.isProcessingQueue) {
            this.processQueue();
        }
    }
    
    async processQueue() {
        if (this.speechQueue.length === 0) {
            this.isProcessingQueue = false;
            return;
        }
        
        this.isProcessingQueue = true;
        
        const { text, type } = this.speechQueue.shift();
        
        try {
            await this.wizardAnimator.speakRecipeStep(text, type);
        } catch (error) {
            console.error('Speech error:', error);
        }
        
        // Process next item in queue
        setTimeout(() => {
            this.processQueue();
        }, 500);
    }
    
    // Public methods for manual control
    
    async speakCustom(text, options = {}) {
        if (!this.isWizardReady) return;
        return this.wizardAnimator.speak(text, options);
    }
    
    triggerAnimation(type) {
        if (!this.isWizardReady) return;
        this.wizardAnimator.triggerAnimation(type);
    }
    
    stopSpeaking() {
        if (!this.isWizardReady) return;
        this.wizardAnimator.stopSpeaking();
        this.speechQueue = [];
        this.isProcessingQueue = false;
    }
    
    setWizardVisibility(visible) {
        const container = document.getElementById('game-wizard-container');
        if (container) {
            container.style.display = visible ? 'block' : 'none';
        }
    }
    
    setWizardPosition(position) {
        const container = document.getElementById('game-wizard-container');
        if (container && position) {
            if (position.top !== undefined) container.style.top = position.top;
            if (position.right !== undefined) container.style.right = position.right;
            if (position.bottom !== undefined) container.style.bottom = position.bottom;
            if (position.left !== undefined) container.style.left = position.left;
        }
    }
    
    // Utility methods for game integration
    
    isReady() {
        return this.isWizardReady;
    }
    
    getAnimator() {
        return this.wizardAnimator;
    }
    
    destroy() {
        this.stopSpeaking();
        
        const container = document.getElementById('game-wizard-container');
        if (container) {
            container.remove();
        }
        
        this.wizardAnimator = null;
        this.isWizardReady = false;
    }
}

// Auto-initialize if game app is available
if (typeof window !== 'undefined' && window.gameApp) {
    window.wizardIntegration = new AlchemyGameWizardIntegration(window.gameApp);
}

// Export for manual initialization
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AlchemyGameWizardIntegration;
}
