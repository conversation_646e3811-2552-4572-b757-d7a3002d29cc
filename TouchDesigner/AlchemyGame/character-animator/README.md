# 🧙‍♂️ Čarodej - Live2D Character Animator

<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> animačný systém pre čarodeja s realistickými Live2D animáciami, lip-sync funkciou a integráciou s text-to-speech.

## ✨ Implementované funkcie

### 🎭 Základné anim<PERSON>
- **Realistické pohyby hlavy** - jemn<PERSON> rot<PERSON>cie a posuny (8s cyklus)
- **<PERSON><PERSON><PERSON> o<PERSON>** - prirodzené mrkanie každé 4 sekundy
- **Pohyby úst pre reč** - základ<PERSON> animácia úst (3s cyklus)
- **Dýchanie** - jemné pohyby tela simulujúce dýchanie (6s cyklus)
- **Obč<PERSON><PERSON><PERSON> naklonenie hlavy** - pre prirodzenosť (12s cyklus)
- **Pohyb očí** - jemn<PERSON> pohyby zren<PERSON>c (7s cyklus)
- **Kolísanie rúcha** - pohy<PERSON> <PERSON> (10s cyklus)

### 🗣️ Text-to-Speech integrácia
- **<PERSON>k<PERSON> lip-sync** - synchroniz<PERSON>cia úst s rečou
- **Phoneme mapping** - mapovanie samohlások na tvary úst
- **Podpora slovenčiny/češtiny** - preferované hlasy
- **Konfigurovateľné parametre** - rýchlosť, výška, hlasitosť

### 🎵 Lip-sync triedy
- `.mouth-a` - otvorené ústa pre "A"
- `.mouth-e` - úzke ústa pre "E" 
- `.mouth-i` - široké ústa pre "I"
- `.mouth-o` - okrúhle ústa pre "O"
- `.mouth-u` - úzke okrúhle ústa pre "U"
- `.mouth-closed` - zatvorené ústa

### 🎪 Interaktívne animácie
- **Pozdrav** - rýchlejšie pohyby hlavy
- **Premýšľanie** - častejšie naklonenie hlavy
- **Nadšenie** - rýchlejšie mrkanie
- **Sklamanie** - pokles tela

## 📁 Súbory

```
character-animator/
├── asset_dNSkJw3NBnEguAg7Y5kdz4Z7_vectorization_1752850777.svg  # SVG s animáciami
├── wizard-animator.js                                              # JavaScript kontrolér
├── wizard-demo.html                                               # Demo stránka
└── README.md                                                      # Dokumentácia
```

## 🚀 Použitie

### 1. Základná inicializácia

```html
<!-- Vložte SVG do HTML -->
<div id="wizard-container"></div>

<!-- Načítajte JavaScript -->
<script src="wizard-animator.js"></script>

<script>
// Inicializujte animator
const animator = new WizardAnimator('wizard-svg');
</script>
```

### 2. Text-to-Speech

```javascript
// Základné použitie
await animator.speak("Vitajte v mojej čarodejníckej dielni!");

// S konfiguráciou
await animator.speak("Ahoj!", {
    rate: 0.8,      // rýchlosť reči
    pitch: 1.2,     // výška hlasu
    volume: 0.9     // hlasitosť
});

// Zastavenie reči
animator.stopSpeaking();
```

### 3. Manuálne animácie

```javascript
// Spustenie špecifických animácií
animator.triggerAnimation('greeting');     // pozdrav
animator.triggerAnimation('thinking');     // premýšľanie
animator.triggerAnimation('excited');      // nadšenie
animator.triggerAnimation('disappointed'); // sklamanie
```

### 4. Manuálny lip-sync

```javascript
// Nastavenie tvaru úst
animator.setMouthShape('a');      // otvorené ústa
animator.setMouthShape('o');      // okrúhle ústa
animator.setMouthShape('closed'); // zatvorené ústa

// Zastavenie lip-sync
animator.stopLipSync();
```

## 🎮 Demo

Otvorte `wizard-demo.html` v prehliadači pre interaktívne demo s:
- Text-to-speech vstupom
- Prednastavenými textami
- Manuálnymi animáciami
- Kontrolami pre testovanie

## 🔧 Technické detaily

### Podporované prehliadače
- Chrome/Edge 71+
- Firefox 62+
- Safari 14+
- (Vyžaduje Web Speech API)

### Phoneme Detection
Systém používa zjednodušenú detekciu phonémov:
- Samohlásky → špecifické tvary úst
- Spoluhlásky → zatvorené ústa
- Automatické časovanie na základe dĺžky slov

### CSS Animácie
Všetky animácie sú implementované pomocí CSS `@keyframes` pre:
- Optimálny výkon
- Plynulé prechody
- Nízku záťaž CPU

## 🎨 Prispôsobenie

### Úprava animácií
Editujte CSS v SVG súbore:

```css
/* Zmena rýchlosti dýchania */
.body-group {
    animation: breathing 4s ease-in-out infinite; /* bolo 6s */
}

/* Pridanie novej animácie */
@keyframes customAnimation {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
```

### Nové lip-sync tvary
Pridajte nové triedy do CSS:

```css
.mouth-smile { 
    transform: scaleY(0.8) scaleX(1.3) rotate(5deg); 
}
.mouth-frown { 
    transform: scaleY(0.6) scaleX(1.1) rotate(-3deg); 
}
```

### Rozšírenie phoneme mapy
```javascript
// V wizard-animator.js
this.phonemeMap = {
    // existujúce...
    'smile': 'mouth-smile',
    'frown': 'mouth-frown'
};
```

## 🐛 Riešenie problémov

### Reč nefunguje
- Skontrolujte podporu Web Speech API
- Povoľte mikrofón v prehliadači
- Skúste iný prehliadač

### Animácie sa nezobrazujú
- Skontrolujte, či je SVG správne načítané
- Overte CSS triedy v SVG
- Skontrolujte konzolu pre chyby

### Lip-sync nie je synchronizovaný
- Upravte časovanie v `animateWordPhonemes()`
- Skúste iné hodnoty `rate` pre speech synthesis
- Testujte s kratšími textami

## 📝 Licencia

Súčasť AlchemyGame projektu - použitie pre vzdelávacie a komerčné účely.

## 🤝 Prispievanie

Pre pridanie nových funkcií alebo opravy:
1. Vytvorte kópiu súborov
2. Implementujte zmeny
3. Otestujte s demo stránkou
4. Dokumentujte zmeny
